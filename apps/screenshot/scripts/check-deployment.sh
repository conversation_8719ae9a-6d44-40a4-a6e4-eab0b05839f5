#!/bin/bash

# Screenshot Service Deployment Check Script
# This script checks if all required services and permissions are available

set -e

echo "🔍 Checking Screenshot Service Deployment Requirements..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Check if required environment variables are set
check_env_var() {
    local var_name=$1
    local var_value=${!var_name}
    
    if [ -z "$var_value" ]; then
        echo -e "${RED}❌ $var_name is not set${NC}"
        return 1
    else
        echo -e "${GREEN}✅ $var_name is set${NC}"
        return 0
    fi
}

# Check Cloudflare API access
check_cloudflare_api() {
    local account_id=$1
    local api_token=$2
    
    echo "🌐 Checking Cloudflare API access..."
    
    local response=$(curl -s -w "%{http_code}" -o /tmp/cf_response \
        -H "Authorization: Bearer $api_token" \
        -H "Content-Type: application/json" \
        "https://api.cloudflare.com/client/v4/accounts/$account_id")
    
    if [ "$response" = "200" ]; then
        echo -e "${GREEN}✅ Cloudflare API access verified${NC}"
        return 0
    else
        echo -e "${RED}❌ Cloudflare API access failed (HTTP $response)${NC}"
        cat /tmp/cf_response
        return 1
    fi
}

# Check Browser Rendering API access
check_browser_rendering_api() {
    local account_id=$1
    local api_token=$2
    
    echo "🖥️ Checking Browser Rendering API access..."
    
    local response=$(curl -s -w "%{http_code}" -o /tmp/br_response \
        -H "Authorization: Bearer $api_token" \
        -H "Content-Type: application/json" \
        "https://api.cloudflare.com/client/v4/accounts/$account_id/browser-rendering")
    
    if [ "$response" = "200" ]; then
        echo -e "${GREEN}✅ Browser Rendering API access verified${NC}"
        return 0
    else
        echo -e "${RED}❌ Browser Rendering API access failed (HTTP $response)${NC}"
        echo -e "${YELLOW}⚠️ Make sure Browser Rendering is enabled in your Cloudflare account${NC}"
        cat /tmp/br_response
        return 1
    fi
}

# Test screenshot API
test_screenshot_api() {
    local account_id=$1
    local api_token=$2
    
    echo "📸 Testing screenshot API with a simple URL..."
    
    local test_payload='{
        "url": "https://example.com",
        "screenshotOptions": {
            "fullPage": false,
            "omitBackground": false
        },
        "viewport": {
            "width": 1280,
            "height": 720
        },
        "gotoOptions": {
            "waitUntil": "networkidle0",
            "timeout": 30000
        }
    }'
    
    local response=$(curl -s -w "%{http_code}" -o /tmp/screenshot_test \
        -X POST \
        -H "Authorization: Bearer $api_token" \
        -H "Content-Type: application/json" \
        -d "$test_payload" \
        "https://api.cloudflare.com/client/v4/accounts/$account_id/browser-rendering/screenshot")
    
    if [ "$response" = "200" ]; then
        echo -e "${GREEN}✅ Screenshot API test successful${NC}"
        return 0
    else
        echo -e "${RED}❌ Screenshot API test failed (HTTP $response)${NC}"
        cat /tmp/screenshot_test
        return 1
    fi
}

# Main execution
main() {
    echo "Starting deployment checks..."
    
    # Load environment variables from .dev.vars if it exists
    if [ -f ".dev.vars" ]; then
        echo "📁 Loading environment variables from .dev.vars..."
        export $(cat .dev.vars | grep -v '^#' | xargs)
    fi
    
    local errors=0
    
    # Check required environment variables
    echo -e "\n📋 Checking environment variables..."
    check_env_var "CLOUDFLARE_ACCOUNT_ID" || ((errors++))
    check_env_var "CLOUDFLARE_API_TOKEN" || ((errors++))
    check_env_var "DATABASE_ID" || ((errors++))
    check_env_var "POSTGRES_URL" || ((errors++))
    
    if [ $errors -gt 0 ]; then
        echo -e "\n${RED}❌ Environment variable checks failed. Please set missing variables.${NC}"
        exit 1
    fi
    
    # Check Cloudflare API access
    echo -e "\n🔐 Checking Cloudflare API access..."
    check_cloudflare_api "$CLOUDFLARE_ACCOUNT_ID" "$CLOUDFLARE_API_TOKEN" || ((errors++))
    
    # Check Browser Rendering API
    echo -e "\n🖥️ Checking Browser Rendering API..."
    check_browser_rendering_api "$CLOUDFLARE_ACCOUNT_ID" "$CLOUDFLARE_API_TOKEN" || ((errors++))
    
    # Test screenshot functionality
    echo -e "\n📸 Testing screenshot functionality..."
    test_screenshot_api "$CLOUDFLARE_ACCOUNT_ID" "$CLOUDFLARE_API_TOKEN" || ((errors++))
    
    # Final result
    if [ $errors -eq 0 ]; then
        echo -e "\n${GREEN}🎉 All checks passed! Screenshot service is ready for deployment.${NC}"
        exit 0
    else
        echo -e "\n${RED}❌ $errors check(s) failed. Please fix the issues before deploying.${NC}"
        exit 1
    fi
}

# Cleanup function
cleanup() {
    rm -f /tmp/cf_response /tmp/br_response /tmp/screenshot_test
}

# Set trap for cleanup
trap cleanup EXIT

# Run main function
main "$@"
