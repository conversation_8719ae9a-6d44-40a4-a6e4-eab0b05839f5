/*
 * SPDX-License-Identifier: AGPL-3.0-only
 * env-validator.ts
 * Copyright (C) 2025 Nextify Limited
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as
 * published by the Free Software Foundation, either version 3 of the
 * License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with this program. If not, see <https://www.gnu.org/licenses/>.
 *
 */

import { ScreenshotError, ErrorCodes } from './errors'
import type { Bindings } from '../types'

/**
 * Environment validation result
 */
export interface EnvValidationResult {
  isValid: boolean
  errors: string[]
  warnings: string[]
}

/**
 * Required environment variables for screenshot service
 */
const REQUIRED_ENV_VARS = [
  'CLOUDFLARE_ACCOUNT_ID',
  'CLOUDFLARE_API_TOKEN',
  'DATABASE_ID',
  'POSTGRES_URL'
] as const

/**
 * Optional environment variables with defaults
 */
const OPTIONAL_ENV_VARS = [
  'SCREENSHOT_QUEUE_NAME',
  'SCREENSHOT_DLQ_NAME',
  'MAX_SCREENSHOT_TIMEOUT',
  'MAX_CONCURRENT_SCREENSHOTS',
  'LOG_LEVEL',
  'ENVIRONMENT'
] as const

/**
 * Validate environment variables for screenshot service
 */
export function validateEnvironment(env: Bindings): EnvValidationResult {
  const errors: string[] = []
  const warnings: string[] = []

  // Check required environment variables
  for (const varName of REQUIRED_ENV_VARS) {
    const value = env[varName]
    if (!value || typeof value !== 'string' || value.trim() === '') {
      errors.push(`Missing required environment variable: ${varName}`)
    } else if (varName === 'CLOUDFLARE_ACCOUNT_ID' && !isValidAccountId(value)) {
      errors.push(`Invalid Cloudflare Account ID format: ${varName}`)
    } else if (varName === 'CLOUDFLARE_API_TOKEN' && !isValidApiToken(value)) {
      errors.push(`Invalid Cloudflare API Token format: ${varName}`)
    } else if (varName === 'POSTGRES_URL' && !isValidPostgresUrl(value)) {
      errors.push(`Invalid PostgreSQL URL format: ${varName}`)
    }
  }

  // Check optional environment variables and provide warnings
  for (const varName of OPTIONAL_ENV_VARS) {
    const value = env[varName]
    if (!value || typeof value !== 'string' || value.trim() === '') {
      warnings.push(`Optional environment variable not set: ${varName} (using default)`)
    }
  }

  // Validate specific configurations
  if (env.MAX_SCREENSHOT_TIMEOUT) {
    const timeout = parseInt(env.MAX_SCREENSHOT_TIMEOUT, 10)
    if (isNaN(timeout) || timeout <= 0) {
      errors.push('MAX_SCREENSHOT_TIMEOUT must be a positive number')
    } else if (timeout > 600000) { // 10 minutes
      warnings.push('MAX_SCREENSHOT_TIMEOUT is very high (>10 minutes), this may cause issues')
    }
  }

  if (env.MAX_CONCURRENT_SCREENSHOTS) {
    const concurrent = parseInt(env.MAX_CONCURRENT_SCREENSHOTS, 10)
    if (isNaN(concurrent) || concurrent <= 0) {
      errors.push('MAX_CONCURRENT_SCREENSHOTS must be a positive number')
    } else if (concurrent > 10) {
      warnings.push('MAX_CONCURRENT_SCREENSHOTS is very high (>10), this may cause resource issues')
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  }
}

/**
 * Validate Cloudflare Account ID format
 */
function isValidAccountId(accountId: string): boolean {
  // Cloudflare Account IDs are 32-character hexadecimal strings
  return /^[a-f0-9]{32}$/i.test(accountId)
}

/**
 * Validate Cloudflare API Token format
 */
function isValidApiToken(token: string): boolean {
  // Cloudflare API tokens are typically 40+ character alphanumeric strings
  return /^[A-Za-z0-9_-]{20,}$/.test(token)
}

/**
 * Validate PostgreSQL URL format
 */
function isValidPostgresUrl(url: string): boolean {
  try {
    const parsed = new URL(url)
    return parsed.protocol === 'postgresql:' || parsed.protocol === 'postgres:'
  } catch {
    return false
  }
}

/**
 * Throw error if environment is invalid
 */
export function validateEnvironmentOrThrow(env: Bindings): void {
  const result = validateEnvironment(env)
  
  if (!result.isValid) {
    throw new ScreenshotError(
      500,
      ErrorCodes.INTERNAL_ERROR,
      `Environment validation failed: ${result.errors.join(', ')}`,
      { errors: result.errors, warnings: result.warnings }
    )
  }
}

/**
 * Get environment variable with fallback
 */
export function getEnvVar(env: Bindings, key: keyof Bindings, fallback?: string): string {
  const value = env[key]
  if (value && typeof value === 'string') {
    return value
  }
  if (fallback !== undefined) {
    return fallback
  }
  throw new ScreenshotError(
    500,
    ErrorCodes.INTERNAL_ERROR,
    `Required environment variable ${String(key)} is not set`
  )
}

/**
 * Get numeric environment variable with validation
 */
export function getNumericEnvVar(
  env: Bindings, 
  key: keyof Bindings, 
  fallback?: number,
  min?: number,
  max?: number
): number {
  const value = env[key]
  let numValue: number
  
  if (value && typeof value === 'string') {
    numValue = parseInt(value, 10)
    if (isNaN(numValue)) {
      if (fallback !== undefined) {
        numValue = fallback
      } else {
        throw new ScreenshotError(
          500,
          ErrorCodes.INTERNAL_ERROR,
          `Environment variable ${String(key)} must be a valid number`
        )
      }
    }
  } else if (fallback !== undefined) {
    numValue = fallback
  } else {
    throw new ScreenshotError(
      500,
      ErrorCodes.INTERNAL_ERROR,
      `Required numeric environment variable ${String(key)} is not set`
    )
  }
  
  // Validate range
  if (min !== undefined && numValue < min) {
    throw new ScreenshotError(
      500,
      ErrorCodes.INTERNAL_ERROR,
      `Environment variable ${String(key)} must be >= ${min}, got ${numValue}`
    )
  }
  
  if (max !== undefined && numValue > max) {
    throw new ScreenshotError(
      500,
      ErrorCodes.INTERNAL_ERROR,
      `Environment variable ${String(key)} must be <= ${max}, got ${numValue}`
    )
  }
  
  return numValue
}

/**
 * Test Cloudflare API connectivity
 */
export async function testCloudflareApiConnectivity(env: Bindings): Promise<{
  success: boolean
  error?: string
  accountInfo?: any
}> {
  try {
    const accountId = getEnvVar(env, 'CLOUDFLARE_ACCOUNT_ID')
    const apiToken = getEnvVar(env, 'CLOUDFLARE_API_TOKEN')
    
    const response = await fetch(
      `https://api.cloudflare.com/client/v4/accounts/${accountId}`,
      {
        headers: {
          'Authorization': `Bearer ${apiToken}`,
          'Content-Type': 'application/json'
        }
      }
    )
    
    if (!response.ok) {
      const errorText = await response.text()
      return {
        success: false,
        error: `Cloudflare API error: ${response.status} ${errorText}`
      }
    }
    
    const data = await response.json()
    return {
      success: true,
      accountInfo: data.result
    }
    
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error)
    }
  }
}

/**
 * Test Browser Rendering API availability
 */
export async function testBrowserRenderingApi(env: Bindings): Promise<{
  success: boolean
  error?: string
  available?: boolean
}> {
  try {
    const accountId = getEnvVar(env, 'CLOUDFLARE_ACCOUNT_ID')
    const apiToken = getEnvVar(env, 'CLOUDFLARE_API_TOKEN')
    
    const response = await fetch(
      `https://api.cloudflare.com/client/v4/accounts/${accountId}/browser-rendering`,
      {
        headers: {
          'Authorization': `Bearer ${apiToken}`,
          'Content-Type': 'application/json'
        }
      }
    )
    
    if (!response.ok) {
      const errorText = await response.text()
      return {
        success: false,
        error: `Browser Rendering API error: ${response.status} ${errorText}`
      }
    }
    
    return {
      success: true,
      available: true
    }
    
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error)
    }
  }
}
